# SynapseAI: Unified Production-Grade System Build Prompt

You are building **SynapseAI**, a **complete, modular, real-time, multi-agent AI orchestration platform** with enterprise-grade features, seamless integration, and developer-first tooling.
You are not writing a prototype. You are building a **real, complete, production-grade system** for SynapseAI. You must **strictly follow** these rules:

---

## 🛑 Absolutely FORBIDDEN

- ❌ No mock data
- ❌ No simulated outputs
- ❌ No hardcoded `useState` arrays
- ❌ No "TODO" comments
- ❌ No `console.log` debugging
- ❌ No "imagine this is connected..."
- ❌ No incomplete or disconnected code snippets
- ❌ No unimplemented functions or empty handlers

---

## Global Rules (Apply to all modules)

- No mocks, no placeholders, no hardcoded or simulated data  
- Real PostgreSQL data access with Prisma ORM and Redis-backed session/memory  
- Full authentication + RBAC + multi-tenant data isolation  
- Strict Zod schema validation on all input/output boundaries  
- Real-time communication via WebSocket APIX protocol with event streaming, retries, fallback, latency scoring  
- Full error handling, logging, and audit trails  
- Responsive, accessible UI with Next.js 14 + Shadcn/UI + TailwindCSS, no custom CSS hacks  
- Automated testing: Vitest/Jest unit tests, Playwright/Cypress E2E tests on critical flows  
- Developer ecosystem with multi-language SDKs, CLI tools, and extensibility hooks  
- CI/CD pipelines, containerized deployment (Docker, Kubernetes), and compliance (GDPR, SOC2, HIPAA)

---

## 1. Hybrid Workflow & Standalone Execution System

- Visual drag-and-drop workflow builder (React Flow inspired https://n8n.io/) supporting agents, tools, and hybrid nodes  
- Supports standalone workflows and hybrid agent-tool orchestration  
- Workflow execution modes: linear, branching, parallel, conditional routing  
- Trigger types: manual, scheduled (cron), webhook/API, event-driven  
- Human-in-the-Loop support with approval steps and intervention points  
- Session context propagation across nodes with unified memory and state  
- Auto-generated Zod schemas for all workflow nodes and inputs/outputs  
- Live monitoring dashboard with execution logs, node status, error tracking, and retry/fallback logic  
- Workflow import/export in JSON/YAML for portability  
- Multi-tenant aware with RBAC controls and quotas per tenant  
- Seamless real-time coordination via APIX events

---

## 2. Tool-Agent Hybrid Module (Crucial)

- **Seamless integration of tools and agents within workflows** allowing dynamic, real-time switching between agent-driven conversational logic and tool executions  
- **Unified session context and memory shared across agents and tools** in hybrid workflows for consistent state and context preservation  
- **Hybrid execution patterns** including:  
  - Agent-first (conversation-driven tool invocation)  
  - Tool-first (data gathering before agent processing)  
  - Parallel execution (agents and tools operate concurrently with synchronization)  
  - Multi-tool orchestration coordinated by agents  
  - Human-in-the-Loop: workflow steps where human approval or input can pause/resume execution  
- **Auto-generated input/output schemas for hybrid nodes** to enable automatic UI form generation and validation  
- Real-time event coordination via APIX to synchronize state and data between agents and tools  
- Role-based access control and tenant-aware configuration ensuring security in hybrid operations  
- Live debugging and tracing for hybrid workflows showing exact flow of execution across agent-tool boundaries  
- Monitoring and analytics on hybrid workflows to measure performance, error rates, and usage patterns  

---

## 3. Agent System

- Multi-type agents: standalone, tool-driven, hybrid, multi-tasking, multi-provider  
- Persistent Redis-backed session memory with pruning and versioning  
- Agent-to-agent collaboration and chaining using shared session context and APIX  
- Dynamic skill/function definitions and templates (50+ pre-built)  
- Agent creation wizard with multi-step forms, real-time test/debug tools  
- Live session UI with chat interface, memory visualization, multi-task progress, and debugging tools  
- Role-based permissions on agents and access control

---

## 4. Tool System

- Modular tools: function callers, retrieval-augmented generation (RAG), external API fetchers, browser automation, DB runners, custom logic  
- Schema-driven input/output validation with Zod  
- Execution metadata, error contracts, logging, and version management  
- Visual tool builder with adaptive forms and logic composition  
- Live tool testing interface  
- Tool library with categorization, search, usage analytics, sharing, and template gallery  
- Multi-tenant aware configuration and permission control

---

## 5. Provider Integration & Smart Routing

- Supports OpenAI, Claude, Gemini, Mistral, Groq, DeepSeek, Hugging Face, Local models (Ollama, LocalAI)  
- Latency-based smart routing with real-time performance scoring  
- Cost optimization algorithms and quota management per provider  
- Fallback chains for provider failures  
- A/B testing framework for model performance comparison  
- Model capability tagging: chat, embedding, vision, function-call, code generation  
- Provider dashboard with API key management, cost tracking, model testing, and health metrics

---

## 6. UAUI Frontend SDK & Embeddable Components

- React-based embeddable SDK with full TypeScript typing  
- Supports React, Vue, Angular, Vanilla JS integrations  
- Multi-language floating assistant widget (voice + text input, real-time DOM highlighting, contextual suggestions)  
- Auto-generated forms/UI from Zod schemas  
- Real-time sync via APIX and RESTful API via Axios  
- Keyboard shortcuts, undo/redo, command palette, onboarding tours, contextual help  
- Clean, modern, accessible UI supporting dark/light modes and glassmorphism design

---

## 7. APIX Protocol (Real-time Event Bus)

- WebSocket-based bidirectional streaming event system with auto-reconnect and message queuing  
- Channels: agent-events, tool-events, workflow-events, provider-events, system-events  
- Event types: tool_call_start/result/error, thinking_status, text_chunk, state_update, request_user_input, session_start/end, error_occurred, fallback_triggered  
- Retry logic, fallback mechanisms, latency scoring, event replay  
- Room-based subscriptions for collaboration and multi-agent coordination

---

## 8. Knowledge & Embedding Layer

- Document ingestion supporting PDF, Word, TXT, HTML, images with OCR  
- Intelligent chunking and metadata extraction  
- Vector embeddings generated via AI providers for semantic search  
- Hybrid search combining keyword and vector similarity with relevance tuning  
- UI for file management, collections, embedding visualization, query test bench

---

## 9. Tenant & Admin Control

- Complete data/config isolation per tenant with row-level security  
- Tenant-specific branding, language packs, provider enable/disable  
- User management with role assignment and visual RBAC matrix  
- Resource quotas, billing integration, audit logs, and system health dashboards

---

## 10. Analytics & Monitoring

- Real-time customizable dashboards for agent, tool, workflow, provider usage and health  
- Predictive analytics, anomaly detection, cost attribution, and performance optimization  
- Exportable reports (PDF, CSV, API) and alerting system

---

## 11. Developer Ecosystem & Third-Party SDKs

- SDKs for JavaScript/TypeScript, Python, Java, Go, .NET with full API coverage and real-time event support  
- CLI tooling for project scaffolding, workflow management, deployment, and testing  
- IDE integrations: VSCode extension, IntelliJ plugin with syntax highlighting and autocompletion  
- Developer hooks for custom runtime logic injections and event processing  
- OpenAPI and GraphQL endpoints, webhook integration, rate limiting

---

## 12. Testing, Deployment & Compliance

- Unit test coverage >80%, integration and E2E tests for critical flows  
- Performance and security testing including penetration tests  
- Docker containerization with Kubernetes orchestration, auto-scaling, load balancing, and SSL everywhere  
- Blue-green deployments, automated backups, disaster recovery, log aggregation, and monitoring alerts  
- GDPR, SOC2, HIPAA, PCI DSS compliance workflows with audit trails  
- Secure coding standards, encryption at rest/in transit, vulnerability scanning

---

## Success Criteria & Non-Functional Requirements

- Sub-second UI response times (<200ms), real-time event latency <100ms  
- Support 10,000+ concurrent active users, 99.9% uptime  
- Zero code required for 99% of user workflows (click-based, no-code)  
- 3-click maximum for any user action, self-explanatory interface  
- Mobile experience parity with desktop  
- Bulletproof multi-tenant data isolation and security

---

Feed this prompt as the **single source of truth** for building SynapseAI — **a true enterprise-grade AI orchestration platform** that leads the world with real-time multi-agent orchestration, hybrid workflows with full tool-agent integration, rich tooling, robust integrations, advanced analytics, and developer-first extensibility.

No shortcuts, no prototypes — **production-ready code, architecture, and UX from day one**.
