{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@monaco-editor/react": "^4.7.0", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@reactflow/node-resizer": "^2.2.14", "@reactflow/node-toolbar": "^1.3.14", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.84.0", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/qrcode": "^1.5.5", "@types/sharp": "^0.31.1", "@types/speakeasy": "^2.0.10", "autoprefixer": "10.4.20", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cors": "^2.8.5", "cron-parser": "^5.3.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "framer-motion": "^12.23.12", "helmet": "^8.1.0", "i18next": "^25.3.2", "ioredis": "^5.7.0", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "monaco-editor": "^0.52.2", "multer": "^2.0.2", "next": "14.2.23", "next-themes": "^0.2.1", "prettier": "^3.3.3", "prisma": "^6.13.0", "qrcode": "^1.5.4", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dom": "^18", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.61.1", "react-hotkeys-hook": "^5.1.0", "react-i18next": "^15.6.1", "react-joyride": "^2.9.3", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.7", "reactflow": "^11.11.4", "redis": "^5.7.0", "sharp": "^0.34.3", "socket.io-client": "^4.8.1", "speakeasy": "^2.0.0", "stripe": "^17.6.0", "tempo-devtools": "^2.0.109", "uuid": "^11.1.0", "vaul": "^1.1.2", "winston": "^3.17.0", "zod": "^4.0.14", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@nestjs/testing": "^11.1.5", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/socket.io-client": "^1.4.36", "@types/uuid": "^10.0.0", "cypress": "^14.5.3", "jest": "^30.0.5", "playwright": "^1.54.2", "postcss": "^8", "supertest": "^7.1.4", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5"}}